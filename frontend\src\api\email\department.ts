import request from '@/utils/request'

// 获取部门列表（分页）
export const getDepartments = (params?: { 
  page?: number
  size?: number
  search?: string 
}) => {
  return request.get('/email/departments', { params })
}

// 获取部门树形结构
export const getDepartmentTree = () => {
  return request.get('/email/departments/tree')
}

// 获取部门详情
export const getDepartment = (deptId: string) => {
  return request.get(`/email/departments/${deptId}`)
}

// 创建部门
export const createDepartment = (data: {
  dept_id: string
  name: string
  parent_id?: string
  order?: number
}, options?: { sync_to_api?: boolean }) => {
  return request.post('/email/departments', data, {
    params: options
  })
}

// 更新部门
export const updateDepartment = (deptId: string, data: {
  name?: string
  parent_id?: string
  order?: number
}, options?: { sync_to_api?: boolean }) => {
  return request.put(`/email/departments/${deptId}`, data, {
    params: options
  })
}

// 删除部门
export const deleteDepartment = (deptId: string, options?: { sync_to_api?: boolean }) => {
  return request.delete(`/email/departments/${deptId}`, {
    params: options
  })
}

// 从API同步部门数据
export const syncDepartmentsFromApi = () => {
  return request.post('/email/sync/departments')
}

// 部门结构同步相关类型定义
export interface DepartmentSyncRequest {
  source: 'all' | 'company' | 'department'
  company_id?: number
  department_id?: number
  mode: 'create_only' | 'create_update' | 'full_sync'
  dry_run?: boolean
  overwrite_existing?: boolean
  create_hierarchy?: boolean
}

export interface DepartmentInfo {
  dept_id: number
  dept_name: string
  dept_hierarchy?: string
  company_id?: number
  company_name?: string
  parent_dept_id?: number
  level: number
}

export interface DepartmentSyncOperation {
  dept_info: DepartmentInfo
  operation: string
  tencent_dept_id?: string
  message: string
  error_code?: number
  parent_tencent_id?: string
}

export interface DepartmentSyncStats {
  total_departments: number
  created_departments: number
  updated_departments: number
  skipped_departments: number
  failed_departments: number
  total_companies: number
  success_rate: number
}

export interface DepartmentSyncResult {
  success: boolean
  message: string
  stats: DepartmentSyncStats
  operations: DepartmentSyncOperation[]
  errors: string[]
  warnings: string[]
  duration: number
  start_time: string
  end_time?: string
}

export interface DepartmentHierarchy {
  dept_id: number
  dept_name: string
  parent_id?: number
  children: DepartmentHierarchy[]
  level: number
  tencent_dept_id?: string
}

export interface EcologyDepartmentStructure {
  success: boolean
  message: string
  total_departments: number
  hierarchy: DepartmentHierarchy[]
  departments: DepartmentInfo[]
}

// 从基础信息同步部门结构到腾讯企业邮箱
export const syncDepartmentsFromPersonnel = (data: DepartmentSyncRequest): Promise<DepartmentSyncResult> => {
  return request.post('/email/sync/departments/from-personnel', data)
}

// 预览从基础信息同步部门结构（试运行模式）
export const previewDepartmentsSyncFromPersonnel = (data: DepartmentSyncRequest): Promise<DepartmentSyncResult> => {
  return request.post('/email/sync/departments/from-personnel/preview', data)
}

// 获取基础信息中的部门结构
export const getEcologyDepartmentStructure = (params?: {
  company_id?: number
  department_id?: number
}): Promise<EcologyDepartmentStructure> => {
  return request.get('/email/departments/ecology-structure', { params })
} 