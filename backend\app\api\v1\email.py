import math
import time
import logging
import io
import csv
import pandas as pd
import uuid
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import Response
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any

from app.database import get_db
from app.utils import get_current_user
from app.models.user import User
from app.models.email import EmailConfig as EmailConfigModel
from app.crud.email import email_department, email_member, email_tag, email_group, email_config
from app.crud import email_sync_log
from app.schemas.email import (
    EmailDepartment, EmailDepartmentCreate, EmailDepartmentUpdate, EmailDepartmentWithMembers,
    EmailMember, EmailMemberCreate, EmailMemberUpdate, EmailMemberWithDepartment,
    EmailTag, EmailTagCreate, EmailTagUpdate,
    EmailGroup, EmailGroupCreate, EmailGroupUpdate,
    EmailConfig, EmailConfigCreate, EmailConfigUpdate,
    PaginatedResponse, EmailMemberPaginatedResponse,
    EmailLoginPermissionUpdate, EmailUserOptionRequest, EmailUserOptionResponse, EmailUserOptionSetRequest,
    EmailMemberActiveUpdate
)
from app.schemas import department_sync as schemas
from app.schemas.email_sync_log import (
    EmailSyncLog, EmailSyncLogCreate, EmailSyncLogList, LatestSyncTimes
)
from app.services.email_api import TencentEmailAPIService
from app.utils.redis_cache import RedisCache
from app.core.cache_config import CacheDataType
from app.api import deps
from app.services.sse_manager import sse_manager

# 初始化logger
logger = logging.getLogger(__name__)

router = APIRouter()

# 创建Redis缓存实例
email_cache = RedisCache()

# 邮箱配置管理
@router.get("/configs", response_model=List[EmailConfig])
async def get_email_configs(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:config:view"]))
):
    """获取邮箱配置列表"""
    
    # API地址
    API_BASE_URL = "https://api.exmail.qq.com/cgi-bin"
    
    # 预设的应用列表
    PRESET_APPS = [
        {"name": "通讯录管理", "key": "contact_manager"},
        {"name": "功能设置", "key": "function_settings"},
        {"name": "单点登录", "key": "sso"},
        {"name": "新邮件提醒", "key": "mail_notification"},
        {"name": "日志查询", "key": "log_query"}
    ]
    
    # 获取数据库中的配置
    configs = email_config.get_multi(db, skip=skip, limit=limit)
    
    # 获取企业ID，如果有配置则使用第一个配置的企业ID，否则使用空字符串
    CORP_ID = configs[0].corp_id if configs else ""
    
    # 创建应用key到配置的映射
    existing_configs = {config.app_key: config for config in configs if config.app_key}
    
    # 构建结果列表，确保包含所有预设应用
    result = []
    for app in PRESET_APPS:
        app_key = app["key"]
        if app_key in existing_configs:
            # 使用现有配置
            result.append(existing_configs[app_key])
        else:
            # 创建新配置对象（但不保存到数据库）
            result.append(EmailConfig(
                id=None,  # 前端会根据id是否存在决定是创建还是更新
                corp_id=CORP_ID,
                corp_secret="",  # 空密钥
                app_name=app["name"],
                app_key=app_key,
                api_base_url=API_BASE_URL,
                is_active=False,
                access_token=None,
                token_expires_at=None,
                created_at=datetime.now(),
                updated_at=datetime.now()
            ))
    
    return result

@router.post("/configs", response_model=EmailConfig)
async def create_email_config(
    config_in: EmailConfigCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:config:create"]))
):
    """创建邮箱配置"""
    
    # 检查应用名称是否已存在
    existing = db.query(EmailConfigModel).filter(EmailConfigModel.app_name == config_in.app_name).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"应用 '{config_in.app_name}' 已存在"
        )
    
    # 固定API地址
    config_in.api_base_url = "https://api.exmail.qq.com/cgi-bin"
    
    # 设置app_key
    app_keys = {
        "通讯录管理": "contact_manager",
        "功能设置": "function_settings",
        "单点登录": "sso",
        "新邮件提醒": "mail_notification",
        "日志查询": "log_query"
    }
    if config_in.app_name in app_keys:
        config_in.app_key = app_keys[config_in.app_name]
    
    config = email_config.create(db, obj_in=config_in)
    return config

@router.put("/configs/{config_id}", response_model=EmailConfig)
async def update_email_config(
    config_id: int,
    config_in: EmailConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:config:update"]))
):
    """更新邮箱配置"""
    
    config = email_config.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    
    # 固定API地址
    if config_in.api_base_url is not None:
        config_in.api_base_url = "https://api.exmail.qq.com/cgi-bin"
    
    config = email_config.update(db, db_obj=config, obj_in=config_in)
    return config

@router.delete("/configs/{config_id}")
async def delete_email_config(
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:config:delete"]))
):
    """删除邮箱配置"""
    config = email_config.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    email_config.remove(db, id=config_id)
    return {"message": "配置删除成功"}

@router.post("/configs/{config_id}/test")
async def test_email_config(
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:config:view"]))
):
    """测试邮箱配置连接"""
    
    config = email_config.get(db, id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在，请先保存配置"
        )
    
    # 检查密钥是否已设置
    if not config.corp_secret:
        return {
            "success": False,
            "message": "企业密钥未设置，请先设置密钥"
        }
    
    try:
        # 临时创建API服务实例进行测试
        from app.services.email_api import TencentEmailAPIService
        
        # 临时设置为活跃配置进行测试
        original_active = config.is_active
        config.is_active = True
        db.add(config)
        db.commit()
        
        try:
            # 使用指定的应用名称创建API服务实例
            api_service = TencentEmailAPIService(db, app_name=config.app_name)
            access_token = await api_service.get_access_token()
            
            return {
                "success": True,
                "message": f"连接测试成功，应用 '{config.app_name}' 配置有效",
                "access_token": access_token[:10] + "..." if access_token else None
            }
        finally:
            # 恢复原始状态
            config.is_active = original_active
            db.add(config)
            db.commit()
            
    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}"
        }

# 部门管理
@router.get("/departments")
async def get_departments(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:view"]))
):
    """获取部门列表（分页）"""
    skip = (page - 1) * size
    
    if search:
        departments = email_department.search(db, name=search, skip=skip, limit=size)
        total = len(email_department.search(db, name=search, skip=0, limit=1000))  # 简单计算总数
    else:
        departments = email_department.get_multi(db, skip=skip, limit=size)
        total = len(email_department.get_multi(db, skip=0, limit=1000))
    
    pages = math.ceil(total / size)
    
    return {
        "items": departments,
        "total": total,
        "page": page,
        "size": size,
        "pages": pages
    }

@router.get("/departments/tree")
async def get_department_tree(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:view"]))
):
    """获取部门树形结构"""
    tree = email_department.get_tree(db)
    return {"tree": tree}

@router.get("/departments/{dept_id}", response_model=EmailDepartmentWithMembers)
async def get_department(
    dept_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:view"]))
):
    """获取部门详情"""
    department = email_department.get_by_dept_id(db, dept_id=dept_id)
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="部门不存在"
        )
    return department

@router.post("/departments", response_model=EmailDepartment)
async def create_department(
    department_in: EmailDepartmentCreate,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:create"]))
):
    """创建部门"""
    # 检查部门ID是否已存在
    existing = email_department.get_by_dept_id(db, dept_id=department_in.dept_id)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="部门ID已存在"
        )
    
    # 如果需要同步到API
    if sync_to_api:
        try:
            api_service = TencentEmailAPIService(db, app_name="通讯录管理")
            api_data = {
                "id": department_in.dept_id,
                "name": department_in.name,
                "order": department_in.order
            }
            if department_in.parent_id:
                api_data["parentid"] = department_in.parent_id
            
            # 记录API调用参数
            logger.info(f"调用腾讯企业邮箱创建部门API，参数: {api_data}")
            
            result = await api_service.create_department(api_data)
            
            # 记录API返回结果
            logger.info(f"腾讯企业邮箱API返回结果: errcode={result.errcode}, errmsg={result.errmsg}")
            
            if result.errcode != 0:
                # 对特定错误码提供更友好的错误提示
                if result.errcode == 45024:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="企业邮箱用户数量已达上限，请联系管理员升级套餐或删除不需要的账户"
                    )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"API创建失败: {result.errmsg} (错误码: {result.errcode})"
                    )
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"调用腾讯企业邮箱API异常: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 创建本地记录
    department = email_department.create(db, obj_in=department_in)
    return department

@router.put("/departments/{dept_id}", response_model=EmailDepartment)
async def update_department(
    dept_id: str,
    department_in: EmailDepartmentUpdate,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:update"]))
):
    """更新部门"""
    department = email_department.get_by_dept_id(db, dept_id=dept_id)
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="部门不存在"
        )
    
    # 如果需要同步到API
    if sync_to_api:
        try:
            api_service = TencentEmailAPIService(db, app_name="通讯录管理")
            api_data = {"id": dept_id}
            if department_in.name is not None:
                api_data["name"] = department_in.name
            if department_in.parent_id is not None:
                api_data["parentid"] = department_in.parent_id
            if department_in.order is not None:
                api_data["order"] = department_in.order
            
            result = await api_service.update_department(api_data)
            if result.errcode != 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"API更新失败: {result.errmsg}"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 更新本地记录
    department = email_department.update(db, db_obj=department, obj_in=department_in)
    return department

@router.delete("/departments/{dept_id}")
async def delete_department(
    dept_id: str,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:delete"]))
):
    """删除部门"""
    department = email_department.get_by_dept_id(db, dept_id=dept_id)
    if not department:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="部门不存在"
        )
    
    # 如果需要同步到API
    if sync_to_api:
        try:
            api_service = TencentEmailAPIService(db, app_name="通讯录管理")
            result = await api_service.delete_department(dept_id)
            if result.errcode != 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"API删除失败: {result.errmsg}"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 删除本地记录
    email_department.remove(db, id=department.id)
    return {"message": "部门删除成功"}

# 成员管理
@router.get("/members", response_model=EmailMemberPaginatedResponse)
async def get_members(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    department_id: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """获取成员列表（分页）"""
    skip = (page - 1) * size
    
    if search:
        members = email_member.search_with_department(db, name=search, skip=skip, limit=size)
        total = email_member.count_by_search(db, name=search)  # 使用准确的count查询
    elif department_id:
        members = email_member.get_by_department_with_department(db, department_id=department_id, skip=skip, limit=size)
        total = email_member.count_by_department(db, department_id=department_id)  # 使用准确的count查询
    else:
        members = email_member.get_multi_with_department(db, skip=skip, limit=size)
        total = email_member.count_all(db)  # 使用准确的count查询
    
    pages = math.ceil(total / size)
    
    return EmailMemberPaginatedResponse(
        items=members,
        total=total,
        page=page,
        size=size,
        pages=pages
    )

@router.get("/members/export")
async def export_members(
    search: Optional[str] = Query(None, description="根据姓名、邮箱过滤"),
    department_id: Optional[str] = Query(None, description="根据部门过滤"),
    format: str = Query("xlsx", description="导出格式: csv, xlsx"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:export"]))
):
    """
    导出成员列表
    支持CSV和XLSX格式
    """
    # 获取成员数据（不分页，获取所有匹配条件的成员）
    members = email_member.get_members_for_export(
        db, 
        search=search, 
        department_id=department_id
    )
    
    # 准备导出数据
    export_data = []
    for member in members:
        member_data = {
            "工号": member.extid,
            "邮箱地址": member.email,
            "姓名": member.name,
            "部门": member.department.name if member.department else "",
            "职位": member.position or "",
            "手机号": member.mobile or "",
            "电话": member.tel or "",
            "POP/SMTP服务": "启用" if member.pop_smtp_enabled else "禁用",
            "IMAP/SMTP服务": "启用" if member.imap_smtp_enabled else "禁用",
            "安全登录": "启用" if member.secure_login_enabled else "禁用",
            "强制安全登录": "启用" if member.force_secure_login else "禁用",
            "状态": "启用" if member.is_active else "禁用",
            "创建时间": member.created_at.strftime("%Y-%m-%d %H:%M:%S") if member.created_at else "",
            "更新时间": member.updated_at.strftime("%Y-%m-%d %H:%M:%S") if member.updated_at else ""
        }
        export_data.append(member_data)
    
    # 生成时间戳
    timestamp = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
    
    # 根据请求的格式返回不同的响应
    if format.lower() == "csv":
        # 创建CSV输出
        output = io.StringIO()
        fieldnames = [
            "工号", "邮箱地址", "姓名", "部门", "职位", "手机号", "电话",
            "POP/SMTP服务", "IMAP/SMTP服务", "安全登录", "强制安全登录",
            "状态", "创建时间", "更新时间"
        ]
        
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(export_data)
        
        # 添加BOM标记，解决Excel打开中文乱码
        csv_content = '\ufeff' + output.getvalue()
        
        # 返回CSV响应
        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename=email_members_{timestamp}.csv"
            }
        )
    
    elif format.lower() == "xlsx":
        try:
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 创建BytesIO对象来存储Excel数据
            output = io.BytesIO()
            
            # 使用pandas的ExcelWriter创建Excel文件
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='成员列表', index=False)
                
                # 获取工作表对象进行格式化
                worksheet = writer.sheets['成员列表']
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 获取Excel文件的字节数据
            output.seek(0)
            excel_content = output.getvalue()
            
            # 返回Excel响应
            return Response(
                content=excel_content,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={
                    "Content-Disposition": f"attachment; filename=email_members_{timestamp}.xlsx"
                }
            )
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Excel文件生成失败: {str(e)}"
            )
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的导出格式，仅支持csv和xlsx"
        )

@router.get("/members/{userid}", response_model=EmailMemberWithDepartment)
async def get_member(
    userid: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """获取成员详情"""
    # 根据邮箱地址（userid）查找成员
    member = email_member.get_by_email(db, email=userid)
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )
    
    # 添加部门信息
    member_dict = {
        'id': member.id,
        'extid': member.extid,  # 允许为None
        'email': member.email,  # 使用email字段代替userid
        'name': member.name,
        'department_id': member.department_id,
        'department_name': member.department.name if member.department else '',
        'position': member.position,
        'mobile': member.mobile,
        'tel': member.tel,
        'cpwd_login': member.cpwd_login,
        'extattr': member.extattr,
        'is_active': member.is_active,
        'created_at': member.created_at,
        'updated_at': member.updated_at
    }
    return member_dict

@router.post("/members", response_model=EmailMember)
async def create_member(
    member_in: EmailMemberCreate,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:create"]))
):
    """创建成员"""
    # 检查工号是否已存在
    existing_member = email_member.get_by_extid(db, extid=member_in.extid)
    if existing_member:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="工号已存在"
        )
    
    # 检查邮箱是否已存在
    existing_email = email_member.get_by_email(db, email=member_in.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱地址已存在"
        )
    
    # 如果需要同步到API
    if sync_to_api:
        try:
            api_service = TencentEmailAPIService(db, app_name="通讯录管理")
            # 确保department_id是整数
            try:
                department_id_int = int(member_in.department_id)
            except (ValueError, TypeError):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"部门ID必须是数字: {member_in.department_id}"
                )
            
            api_data = {
                "userid": member_in.email,  # 腾讯API的userid参数值为邮箱地址
                "name": member_in.name,
                "department": [department_id_int],  # 修复：确保是整数数组
                "password": member_in.password,  # 必需字段：密码
            }
            
            # 添加可选字段
            if member_in.position:
                api_data["position"] = member_in.position
            if member_in.mobile:
                api_data["mobile"] = member_in.mobile
            if member_in.tel:
                api_data["tel"] = member_in.tel
            if member_in.cpwd_login is not None:
                api_data["cpwd_login"] = member_in.cpwd_login
            if member_in.extattr:
                api_data["extattr"] = member_in.extattr
            if member_in.extid:
                api_data["extid"] = member_in.extid  # 腾讯API的可选字段：工号
            
            # 记录API调用参数（注意：敏感信息已过滤）
            safe_data = {k: v for k, v in api_data.items() if k != "password"}
            logger.info(f"调用腾讯企业邮箱创建成员API，参数: {safe_data}")
            
            # 调用API
            result = await api_service.create_member(api_data)
            
            # 记录API返回结果
            logger.info(f"腾讯企业邮箱API返回结果: errcode={result.errcode}, errmsg={result.errmsg}")
            
            if result.errcode != 0:
                # 对特定错误码提供更友好的错误提示
                if result.errcode == 45024:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="企业邮箱用户数量已达上限，请联系管理员升级套餐或删除不需要的账户"
                    )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"API创建失败: {result.errmsg} (错误码: {result.errcode})"
                    )
                
            # 创建成功后，设置默认的登录权限（POP和IMAP默认开启）
            try:
                # 使用功能设置应用的配置来管理登录权限
                function_api_service = TencentEmailAPIService(db, app_name="功能设置")
                default_options = [
                    {"type": 1, "value": str(member_in.force_secure_login)},  # 强制安全登录
                    {"type": 2, "value": str(member_in.imap_smtp_enabled)},   # IMAP/SMTP服务
                    {"type": 3, "value": str(member_in.pop_smtp_enabled)},    # POP/SMTP服务
                    {"type": 4, "value": str(member_in.secure_login_enabled)} # 安全登录
                ]
                
                option_result = await function_api_service.set_user_option(member_in.email, default_options)
                if option_result.errcode != 0:
                    logger.warning(f"设置默认登录权限失败: {option_result.errmsg}")
                else:
                    logger.info("成功设置默认登录权限")
                    
            except Exception as e:
                logger.warning(f"设置默认登录权限时发生异常: {str(e)}")
                
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"调用腾讯企业邮箱API异常: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 创建本地记录
    member = email_member.create(db, obj_in=member_in)
    
    # 清除相关缓存
    try:
        # 清除成员列表缓存
        email_cache.clear_pattern(f"request_cache:/api/v1/email/members*")
        
        # 清除部门成员列表缓存（因为新增成员会影响部门成员统计）
        if member.department_id:
            email_cache.clear_pattern(f"request_cache:/api/v1/email/departments*")
        
        logger.info(f"已清除成员创建相关缓存: {member.email}")
    except Exception as e:
        # 缓存清理失败不应该影响主要业务流程，只记录日志
        logger.warning(f"清除成员创建相关缓存失败: {str(e)}")
    
    return member

@router.put("/members/{userid}", response_model=EmailMember)
async def update_member(
    userid: str,
    member_in: EmailMemberUpdate,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:update"]))
):
    """更新成员"""
    # 根据邮箱地址（userid）查找成员
    member = email_member.get_by_email(db, email=userid)
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )
    
    # 如果需要同步到API
    if sync_to_api:
        try:
            api_service = TencentEmailAPIService(db, app_name="通讯录管理")
            api_data = {"userid": member.email}  # 腾讯API的userid参数值为邮箱地址
            if member_in.name is not None:
                api_data["name"] = member_in.name
            if member_in.department_id is not None:
                # 确保department_id是整数
                try:
                    department_id_int = int(member_in.department_id)
                    api_data["department"] = [department_id_int]
                except (ValueError, TypeError):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"部门ID必须是数字: {member_in.department_id}"
                    )
            if member_in.position is not None:
                api_data["position"] = member_in.position
            if member_in.mobile is not None:
                api_data["mobile"] = member_in.mobile
            if member_in.tel is not None:
                api_data["tel"] = member_in.tel
            if member_in.email is not None:
                api_data["userid"] = member_in.email  # 更新腾讯API的userid参数
            if member_in.cpwd_login is not None:
                api_data["cpwd_login"] = member_in.cpwd_login
            if member_in.extattr is not None:
                api_data["extattr"] = member_in.extattr
            if member_in.extid is not None:
                api_data["extid"] = member_in.extid  # 腾讯API的可选字段：工号
            if member_in.password is not None:
                api_data["password"] = member_in.password  # 更新密码字段
            
            # 记录API调用参数（注意：敏感信息已过滤）
            safe_data = {k: v for k, v in api_data.items() if k != "password"}
            logger.info(f"调用腾讯企业邮箱更新成员API，参数: {safe_data}")
            
            result = await api_service.update_member(api_data)
            
            # 记录API返回结果
            logger.info(f"腾讯企业邮箱API返回结果: errcode={result.errcode}, errmsg={result.errmsg}")
            
            if result.errcode != 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"API更新失败: {result.errmsg} (错误码: {result.errcode})"
                )
            
            # 如果有权限字段更新，需要单独调用权限设置API
            permission_options = []
            if member_in.force_secure_login is not None:
                permission_options.append({"type": 1, "value": str(member_in.force_secure_login)})
            if member_in.imap_smtp_enabled is not None:
                permission_options.append({"type": 2, "value": str(member_in.imap_smtp_enabled)})
            if member_in.pop_smtp_enabled is not None:
                permission_options.append({"type": 3, "value": str(member_in.pop_smtp_enabled)})
            if member_in.secure_login_enabled is not None:
                permission_options.append({"type": 4, "value": str(member_in.secure_login_enabled)})
            
            # 如果有权限更新，调用权限设置API
            if permission_options:
                try:
                    # 使用功能设置应用的配置来管理登录权限
                    function_api_service = TencentEmailAPIService(db, app_name="功能设置")
                    permission_result = await function_api_service.set_user_option(member.email, permission_options)
                    
                    logger.info(f"权限设置API返回结果: errcode={permission_result.errcode}, errmsg={permission_result.errmsg}")
                    
                    if permission_result.errcode != 0:
                        logger.warning(f"权限设置失败: {permission_result.errmsg} (错误码: {permission_result.errcode})")
                        # 权限设置失败不影响成员基本信息更新，只记录警告
                except Exception as e:
                    logger.warning(f"权限设置API调用异常: {str(e)}")
                    # 权限设置异常不影响成员基本信息更新，只记录警告
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"调用腾讯企业邮箱API异常: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 更新本地记录
    member = email_member.update(db, db_obj=member, obj_in=member_in)
    
    # 清除相关缓存（使用新的智能缓存系统）
    try:
        # 使用业务键清除缓存，系统会自动选择合适的TTL和依赖关系
        email_cache.clear_pattern("request_cache:/api/v1/email/members*")
        email_cache.clear_pattern("request_cache:/api/v1/email/departments*")
        email_cache.delete(f"request_cache:/api/v1/email/members/{userid}")
        email_cache.clear_pattern(f"request_cache:/api/v1/email/members/{userid}/login-permissions*")
        
        logger.info(f"已清除成员更新相关缓存: {userid}")
    except Exception as e:
        # 缓存清理失败不应该影响主要业务流程，只记录日志
        logger.warning(f"清除成员更新相关缓存失败: {str(e)}")
    
    return member

@router.delete("/members/{userid}")
async def delete_member(
    userid: str,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:delete"]))
):
    """删除成员"""
    # 根据邮箱地址（userid）查找成员
    member = email_member.get_by_email(db, email=userid)
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )
    
    # 如果需要同步到API
    if sync_to_api:
        try:
            api_service = TencentEmailAPIService(db, app_name="通讯录管理")
            # 调用腾讯API删除成员，userid参数值为邮箱地址
            result = await api_service.delete_member(member.email)
            if result.errcode != 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"API删除失败: {result.errmsg}"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 删除本地记录
    email_member.remove(db, id=member.id)
    
    # 清除相关缓存
    try:
        # 清除成员相关的缓存
        email_cache.clear_pattern(f"request_cache:/api/v1/email/members*")
        
        # 清除部门成员列表缓存（因为删除成员会影响部门成员统计）
        if member.department_id:
            email_cache.clear_pattern(f"request_cache:/api/v1/email/departments*")
        
        # 清除特定成员的缓存
        email_cache.delete(f"request_cache:/api/v1/email/members/{userid}")
        
        # 清除成员登录权限相关缓存
        email_cache.clear_pattern(f"request_cache:/api/v1/email/members/{userid}/login-permissions*")
        
        logger.info(f"已清除成员删除相关缓存: {userid}")
    except Exception as e:
        # 缓存清理失败不应该影响主要业务流程，只记录日志
        logger.warning(f"清除成员删除相关缓存失败: {str(e)}")
    
    return {"message": "成员删除成功"}

@router.patch("/members/{userid}/active", response_model=EmailMember)
async def update_member_active(
    userid: str,
    active_update: EmailMemberActiveUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:active"]))
):
    """
    启用或禁用成员
    """
    # 根据邮箱地址（userid）查找成员
    member = email_member.get_by_email(db, email=userid)
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )
    
    # 调用腾讯企业邮箱API进行启用/禁用操作
    try:
        api_service = TencentEmailAPIService(db, app_name="通讯录管理")
        
        if active_update.is_active:
            # 启用成员
            result = await api_service.enable_member(userid)
            action = "启用"
        else:
            # 禁用成员
            result = await api_service.disable_member(userid)
            action = "禁用"
        
        # 检查API调用结果
        if result.errcode != 0:
            logger.error(f"腾讯API{action}成员失败: {result.errmsg} (错误码: {result.errcode})")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"腾讯API{action}成员失败: {result.errmsg}"
            )
        
        logger.info(f"腾讯API{action}成员成功: {userid}")
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"调用腾讯API{action}成员时发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"调用腾讯API时发生错误: {str(e)}"
        )
    
    # API调用成功后，更新本地数据库中的成员状态
    member_update = EmailMemberUpdate(is_active=active_update.is_active)
    member = email_member.update(db, db_obj=member, obj_in=member_update)
    
    # 清除相关缓存
    try:
        # 清除成员列表缓存
        email_cache.clear_pattern(f"request_cache:/api/v1/email/members*")
        
        # 清除部门成员列表缓存（因为状态变更会影响统计）
        if member.department_id:
            email_cache.clear_pattern(f"request_cache:/api/v1/email/departments*")
        
        # 清除特定成员的缓存
        email_cache.delete(f"request_cache:/api/v1/email/members/{userid}")
        
        logger.info(f"已清除成员状态切换相关缓存: {userid}")
    except Exception as e:
        # 缓存清理失败不应该影响主要业务流程，只记录日志
        logger.warning(f"清除成员状态切换相关缓存失败: {str(e)}")
    
    return member

# 标签管理
@router.get("/tags")
async def get_tags(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:tag:view"]))
):
    """获取标签列表（分页）"""
    skip = (page - 1) * size
    
    tags = email_tag.get_multi(db, skip=skip, limit=size)
    total = len(email_tag.get_multi(db, skip=0, limit=1000))  # 简单计算总数
    
    pages = math.ceil(total / size)
    
    return {
        "items": tags,
        "total": total,
        "page": page,
        "size": size,
        "pages": pages
    }

@router.post("/tags", response_model=EmailTag)
async def create_tag(
    tag_in: EmailTagCreate,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:tag:create"]))
):
    """创建标签"""
    # 检查标签ID是否已存在
    existing = email_tag.get_by_tagid(db, tagid=tag_in.tagid)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="标签ID已存在"
        )
    
    # 如果需要同步到API
    if sync_to_api:
        try:
            api_service = TencentEmailAPIService(db, app_name="通讯录管理")
            api_data = {
                "tagid": tag_in.tagid,
                "tagname": tag_in.tagname
            }
            
            result = await api_service.create_tag(api_data)
            if result.errcode != 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"API创建失败: {result.errmsg}"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 创建本地记录
    tag = email_tag.create(db, obj_in=tag_in)
    return tag

# 邮件群组管理
@router.get("/groups")
async def get_groups(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:group:view"]))
):
    """获取群组列表（分页）"""
    skip = (page - 1) * size
    
    if search:
        groups = email_group.search(db, keyword=search, skip=skip, limit=size)
        total = len(email_group.search(db, keyword=search, skip=0, limit=1000))  # 简单计算总数
    else:
        groups = email_group.get_multi(db, skip=skip, limit=size)
        total = len(email_group.get_multi(db, skip=0, limit=1000))
    
    pages = math.ceil(total / size)
    
    return {
        "items": groups,
        "total": total,
        "page": page,
        "size": size,
        "pages": pages
    }

@router.post("/groups", response_model=EmailGroup)
async def create_group(
    group_in: EmailGroupCreate,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:group:create"]))
):
    """创建邮件群组"""
    # 检查群组ID是否已存在
    existing = email_group.get_by_groupid(db, groupid=group_in.groupid)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="群组ID已存在"
        )
    
    # 如果需要同步到API
    if sync_to_api:
        try:
            api_service = TencentEmailAPIService(db, app_name="通讯录管理")
            api_data = {
                "groupid": group_in.groupid,
                "groupname": group_in.groupname
            }
            if group_in.userlist:
                api_data["userlist"] = group_in.userlist.split(",")
            if group_in.groupdesc:
                api_data["groupdesc"] = group_in.groupdesc
            
            result = await api_service.create_group(api_data)
            if result.errcode != 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"API创建失败: {result.errmsg}"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 创建本地记录
    group = email_group.create(db, obj_in=group_in)
    return group

# 同步功能
@router.post("/sync/departments")
async def sync_departments_from_api(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:sync"]))
):
    """从API同步部门数据"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 记录开始时间
    start_time = time.time()
    sync_log_data = {
        "sync_type": "departments",
        "status": "failed",
        "message": "",
        "synced_count": 0,
        "updated_count": 0,
        "total_count": 0,
        "duration": "",
        "details": None,
        "error_message": ""
    }
    
    try:
        api_service = TencentEmailAPIService(db, app_name="通讯录管理")
        result = await api_service.get_department_list()
        
        if result.errcode != 0:
            sync_log_data["error_message"] = f"API调用失败: {result.errmsg}"
            sync_log_data["message"] = sync_log_data["error_message"]
            # 记录失败日志
            email_sync_log.create_sync_log(db, EmailSyncLogCreate(**sync_log_data))
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"API调用失败: {result.errmsg}"
            )
        
        departments_data = result.data.get("department", [])
        synced_count = 0
        updated_count = 0
        
        # 按层级排序部门数据，确保父部门先于子部门创建
        def get_dept_level(dept):
            """计算部门层级深度"""
            if not dept.get("parentid") or dept.get("parentid") == 1:
                return 0  # 根部门
            
            # 查找父部门在列表中的位置，递归计算层级
            parent_id = dept.get("parentid")
            for parent_dept in departments_data:
                if parent_dept.get("id") == parent_id:
                    return 1 + get_dept_level(parent_dept)
            return 1  # 找不到父部门时默认为1级
        
        # 按层级深度排序
        departments_data_sorted = sorted(departments_data, key=get_dept_level)
        
        # 分批处理部门数据
        batch_size = 50  # 每批处理的部门数量
        total_depts = len(departments_data_sorted)
        sync_log_data["total_count"] = total_depts
        
        logger.info(f"开始同步部门，共 {total_depts} 个部门（已按层级排序）")
        
        for i in range(0, total_depts, batch_size):
            batch_depts = departments_data_sorted[i:i+batch_size]
            logger.info(f"处理部门批次 {i//batch_size + 1}/{(total_depts + batch_size - 1)//batch_size}，包含 {len(batch_depts)} 个部门")
            
            batch_synced = 0
            batch_updated = 0
            for dept_data in batch_depts:
                dept_id = str(dept_data.get("id"))
                existing = email_department.get_by_dept_id(db, dept_id=dept_id)
                
                if not existing:
                    # 处理父部门ID：企业邮箱API中1表示根部门，在本地数据库中用None表示
                    parent_id = dept_data.get("parentid")
                    if parent_id == 1 or parent_id is None:
                        local_parent_id = None  # 根部门
                    else:
                        local_parent_id = str(parent_id)
                    
                    dept_create = EmailDepartmentCreate(
                        dept_id=dept_id,
                        name=dept_data.get("name", ""),
                        parent_id=local_parent_id,
                        order=dept_data.get("order", 0)
                    )
                    email_department.create(db, obj_in=dept_create)
                    batch_synced += 1
                    logger.debug(f"创建部门: {dept_data.get('name')} (ID: {dept_id}, 父部门: {local_parent_id})")
                else:
                    # 更新现有部门
                    parent_id = dept_data.get("parentid")
                    if parent_id == 1 or parent_id is None:
                        local_parent_id = None  # 根部门
                    else:
                        local_parent_id = str(parent_id)
                    
                    dept_update = EmailDepartmentUpdate(
                        name=dept_data.get("name"),
                        parent_id=local_parent_id,
                        order=dept_data.get("order", 0)
                    )
                    email_department.update(db, db_obj=existing, obj_in=dept_update)
                    batch_updated += 1
                    logger.debug(f"更新部门: {dept_data.get('name')} (ID: {dept_id}, 父部门: {local_parent_id})")
            
            # 每批次处理完成后提交事务
            if batch_synced > 0 or batch_updated > 0:
                db.commit()
                synced_count += batch_synced
                updated_count += batch_updated
                logger.info(f"本批次同步了 {batch_synced} 个部门，更新了 {batch_updated} 个部门")
        
        # 计算总耗时
        elapsed_time = time.time() - start_time
        
        # 记录成功日志
        sync_log_data.update({
            "status": "success",
            "message": f"成功同步 {synced_count} 个部门，更新 {updated_count} 个部门",
            "synced_count": synced_count,
            "updated_count": updated_count,
            "duration": f"{elapsed_time:.2f}秒"
        })
        email_sync_log.create_sync_log(db, EmailSyncLogCreate(**sync_log_data))
        
        return {
            "message": f"成功同步 {synced_count} 个部门，更新 {updated_count} 个部门，总耗时: {elapsed_time:.2f}秒",
            "synced_count": synced_count,
            "updated_count": updated_count,
            "total_count": total_depts,
            "elapsed_time": f"{elapsed_time:.2f}秒"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        # 发生异常时回滚事务
        db.rollback()
        logger.error(f"同步部门失败: {str(e)}", exc_info=True)
        
        # 记录错误日志
        elapsed_time = time.time() - start_time
        sync_log_data.update({
            "status": "failed",
            "message": f"同步失败: {str(e)}",
            "error_message": str(e),
            "duration": f"{elapsed_time:.2f}秒"
        })
        email_sync_log.create_sync_log(db, EmailSyncLogCreate(**sync_log_data))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步失败: {str(e)}"
        )

@router.post("/sync/members")
async def sync_members_from_api(
    department_id: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:sync"]))
):
    """从API同步成员数据"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限"
        )
    
    # 生成唯一的同步ID用于进度跟踪
    sync_id = f"member_sync_{int(time.time())}_{str(uuid.uuid4())[:8]}"
    
    # 导入锁服务
    from app.services.email_sync_lock import acquire_lock, release_lock, check_conflicting_operations, LOCK_TYPES
    
    # 检查是否有冲突的操作正在进行
    conflicts = await check_conflicting_operations(db, LOCK_TYPES["CACHE_SYNC"])
    if conflicts:
        conflict_info = ", ".join([f"{c['operation_type']}({c['locked_by']})" for c in conflicts["conflicts"]])
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"无法执行缓存同步，有冲突的操作正在进行：{conflict_info}"
        )
    
    # 尝试获取锁
    lock_name = "email_cache_sync"
    lock_acquired = await acquire_lock(db, lock_name, LOCK_TYPES["CACHE_SYNC"], current_user.username)
    
    if not lock_acquired:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="邮箱缓存同步正在进行中，请稍后再试"
        )
    
    try:
        # 发送同步开始消息
        await sse_manager.broadcast_email_sync_progress(sync_id, {
            "stage": "initializing",
            "stage_name": "初始化同步",
            "progress_percentage": 0,
            "status": "running",
            "message": "正在初始化邮箱成员同步..."
        })
        
        # 使用通讯录管理应用配置（具有通讯录权限）
        api_service = TencentEmailAPIService(db, app_name="通讯录管理")
        
        # 发送API调用开始消息
        await sse_manager.broadcast_email_sync_progress(sync_id, {
            "stage": "fetching_data",
            "stage_name": "获取成员数据",
            "progress_percentage": 10,
            "status": "running",
            "message": "正在从腾讯企业邮箱API获取成员数据..."
        })
        
        if department_id:
            result = await api_service.get_department_members_detail(department_id, fetch_child=1)
        else:
            # 如果没有指定部门，同步根部门
            result = await api_service.get_department_members_detail("1", fetch_child=1)
        
        if result.errcode != 0:
            await sse_manager.broadcast_email_sync_progress(sync_id, {
                "stage": "error",
                "stage_name": "同步失败",
                "progress_percentage": 0,
                "status": "failed",
                "message": f"API调用失败: {result.errmsg}",
                "error": result.errmsg
            })
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"API调用失败: {result.errmsg}"
            )
        
        members_data = result.data.get("userlist", [])
        synced_count = 0
        updated_count = 0
        
        # 优化1: 分批处理成员数据 - 减少批量大小避免超时
        batch_size = 20  # 每批处理的成员数量，从50减少到20
        total_members = len(members_data)
        total_batches = (total_members + batch_size - 1) // batch_size
        
        # 发送数据获取完成消息
        await sse_manager.broadcast_email_sync_progress(sync_id, {
            "stage": "data_fetched",
            "stage_name": "数据获取完成",
            "progress_percentage": 20,
            "status": "running",
            "message": f"成功获取到 {total_members} 个成员数据，共分 {total_batches} 批处理",
            "total_members": total_members,
            "total_batches": total_batches
        })
        
        # 记录开始时间
        start_time = time.time()
        
        # 用于跟踪已使用的extid，避免重复
        used_extids = set()
        existing_members = email_member.get_multi(db)
        for member in existing_members:
            if member.extid:
                used_extids.add(member.extid)
        
        # 优化2: 收集所有用户ID，用于批量获取权限
        all_userids = [member.get("userid") for member in members_data if member.get("userid")]
        
        # 优化3: 批量获取所有用户的权限信息
        function_api_service = TencentEmailAPIService(db, app_name="功能设置")
        permissions_results = {}
        
        try:
            # 使用批量方法获取所有用户的权限信息 - 减少并发数避免超时
            permissions_results = await function_api_service.get_users_options_batch(
                all_userids, [1, 2, 3, 4], batch_size=20, max_concurrent=3
            )
            logger.info(f"批量获取了 {len(permissions_results)} 个用户的权限信息")
            
            # 检查权限获取结果，记录失败的用户
            failed_users = []
            for userid, result in permissions_results.items():
                if result.errcode != 0:
                    failed_users.append(f"{userid}: {result.errmsg}")
            
            if failed_users:
                logger.warning(f"部分用户权限获取失败: {failed_users[:5]}")  # 只记录前5个失败的用户
                if len(failed_users) > 5:
                    logger.warning(f"还有 {len(failed_users) - 5} 个用户权限获取失败")
                    
        except Exception as e:
            logger.error(f"批量获取权限信息失败: {str(e)}")
            # 即使批量获取失败，我们仍然继续同步基本信息
        
        # 分批处理成员数据
        for i in range(0, total_members, batch_size):
            batch_members = members_data[i:i+batch_size]
            current_batch = i//batch_size + 1
            
            # 发送批次处理开始消息
            await sse_manager.broadcast_email_sync_progress(sync_id, {
                "stage": "processing_members",
                "stage_name": "处理成员数据",
                "current_batch": current_batch,
                "total_batches": total_batches,
                "processed_count": synced_count + updated_count,
                "success_count": synced_count,
                "updated_count": updated_count,
                "progress_percentage": 20 + int((current_batch - 1) / total_batches * 60),  # 20%-80%
                "status": "running",
                "message": f"正在处理第 {current_batch}/{total_batches} 批成员数据（{len(batch_members)} 个成员）..."
            })
            
            logger.info(f"处理成员批次 {current_batch}/{total_batches}，包含 {len(batch_members)} 个成员")
            
            # 批量处理当前批次的成员
            batch_processed = 0
            for member_data in batch_members:
                batch_processed += 1
                # 腾讯API返回的userid字段值为邮箱地址
                userid = member_data.get("userid")
                if not userid:
                    continue
                    
                existing = email_member.get_by_userid(db, userid=userid)
                
                if not existing:
                    # 获取第一个部门作为主部门
                    departments = member_data.get("department", [])
                    main_dept = str(departments[0]) if departments else "1"
                    
                    # 处理extid：如果为空或空字符串，则设置为None（NULL）
                    extid = member_data.get("extid")
                    if not extid or extid.strip() == "":
                        extid = None
                    
                    # 防止extid重复：只对非空extid进行重复检查
                    if extid is not None:
                        original_extid = extid
                        counter = 1
                        while extid in used_extids:
                            extid = f"{original_extid}_{counter}"
                            counter += 1
                        used_extids.add(extid)
                    
                    member_create = EmailMemberCreate(
                        extid=extid,  # 使用处理后的extid
                        email=userid,  # 将腾讯API的userid值存储为email字段
                        name=member_data.get("name", ""),
                        department_id=main_dept,
                        password="TempPass123!",  # 同步时使用临时密码，用户需要重置
                        position=member_data.get("position"),
                        mobile=member_data.get("mobile"),
                        tel=member_data.get("tel"),
                        cpwd_login=member_data.get("cpwd_login", 1),
                        extattr=member_data.get("extattr")
                    )
                    new_member = email_member.create(db, obj_in=member_create)
                    synced_count += 1
                    
                    # 使用批量获取的权限数据
                    if userid in permissions_results:
                        try:
                            permission_result = permissions_results[userid]
                            if permission_result.errcode == 0 and permission_result.data:
                                options = permission_result.data.get("option", [])
                                update_data = {}
                                
                                for option in options:
                                    option_type = option.get("type")
                                    value = int(option.get("value", "0"))
                                    
                                    if option_type == 1:
                                        update_data["force_secure_login"] = value
                                    elif option_type == 2:
                                        update_data["imap_smtp_enabled"] = value
                                    elif option_type == 3:
                                        update_data["pop_smtp_enabled"] = value
                                    elif option_type == 4:
                                        update_data["secure_login_enabled"] = value
                                
                                # 更新新成员的权限字段
                                if update_data:
                                    for field, value in update_data.items():
                                        setattr(new_member, field, value)
                                    db.add(new_member)
                                    db.commit()
                            else:
                                # 记录权限获取失败，但不影响成员创建
                                logger.warning(f"新成员 {userid} 权限获取失败: {permission_result.errmsg}")
                        except Exception as e:
                            # 记录权限同步错误但不影响成员同步
                            logger.warning(f"同步新成员 {userid} 的登录权限失败: {str(e)}")
                else:
                    # 对于现有成员，同步登录权限状态
                    if userid in permissions_results:
                        try:
                            permission_result = permissions_results[userid]
                            if permission_result.errcode == 0 and permission_result.data:
                                options = permission_result.data.get("option", [])
                                update_data = {}
                                
                                for option in options:
                                    option_type = option.get("type")
                                    value = int(option.get("value", "0"))
                                    
                                    if option_type == 1:
                                        update_data["force_secure_login"] = value
                                    elif option_type == 2:
                                        update_data["imap_smtp_enabled"] = value
                                    elif option_type == 3:
                                        update_data["pop_smtp_enabled"] = value
                                    elif option_type == 4:
                                        update_data["secure_login_enabled"] = value
                                
                                # 更新现有成员的权限字段
                                if update_data:
                                    for field, value in update_data.items():
                                        setattr(existing, field, value)
                                    db.add(existing)
                                    db.commit()
                                    updated_count += 1
                            else:
                                # 记录权限获取失败，但不影响成员更新
                                logger.warning(f"现有成员 {userid} 权限获取失败: {permission_result.errmsg}")
                        except Exception as e:
                            # 记录权限同步错误但不影响成员同步
                            logger.warning(f"同步现有成员 {userid} 的登录权限失败: {str(e)}")
            
            # 每批处理完成后提交事务，避免事务过大
            db.commit()
            
            # 发送批次完成消息
            await sse_manager.broadcast_email_sync_progress(sync_id, {
                "stage": "batch_completed",
                "stage_name": "批次处理完成",
                "current_batch": current_batch,
                "total_batches": total_batches,
                "batch_processed": batch_processed,
                "total_processed": synced_count + updated_count,
                "synced_count": synced_count,
                "updated_count": updated_count,
                "progress_percentage": 20 + int(current_batch / total_batches * 60),  # 20%-80%
                "status": "running",
                "message": f"第 {current_batch}/{total_batches} 批处理完成，本批处理 {batch_processed} 个成员"
            })
        
        # 计算总耗时
        elapsed_time = time.time() - start_time
        
        # 统计权限同步结果
        permission_success_count = sum(1 for result in permissions_results.values() if result.errcode == 0)
        permission_failed_count = len(permissions_results) - permission_success_count
        
        message = f"成功同步 {synced_count} 个新成员"
        if updated_count > 0:
            message += f"，更新 {updated_count} 个现有成员的登录权限"
        
        # 添加权限同步统计信息
        if permission_success_count > 0:
            message += f"，成功获取 {permission_success_count} 个用户的权限信息"
        if permission_failed_count > 0:
            message += f"，{permission_failed_count} 个用户权限获取失败（可能是'功能设置'应用缺少用户查看权限）"
            
        message += f"，总耗时: {elapsed_time:.2f}秒"
        
        # 发送同步完成消息
        await sse_manager.broadcast_email_sync_progress(sync_id, {
            "stage": "completed",
            "stage_name": "同步完成",
            "progress_percentage": 100,
            "status": "success",
            "message": message,
            "synced_count": synced_count,
            "updated_count": updated_count,
            "total_members": total_members,
            "elapsed_time": elapsed_time,
            "permission_success_count": permission_success_count,
            "permission_failed_count": permission_failed_count
        })
        
        return {"message": message}
        
    except Exception as e:
        # 发生异常时回滚事务
        db.rollback()
        
        # 发送同步失败消息
        await sse_manager.broadcast_email_sync_progress(sync_id, {
            "stage": "error",
            "stage_name": "同步失败",
            "progress_percentage": 0,
            "status": "failed",
            "message": f"同步过程中发生错误: {str(e)}",
            "error": str(e)
        })
        
        logger.error(f"同步失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步失败: {str(e)}"
        )
    finally:
        # 无论成功还是失败，都要释放锁
        await release_lock(db, lock_name, current_user.username)

# 登录权限管理API
@router.get("/members/{userid}/login-permissions", response_model=EmailUserOptionResponse)
async def get_member_login_permissions(
    userid: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """获取成员登录权限设置"""
    # 根据邮箱地址（userid）查找成员
    member = email_member.get_by_email(db, email=userid)
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )
    
    try:
        # 使用功能设置应用的配置来管理登录权限
        api_service = TencentEmailAPIService(db, app_name="功能设置")
        # 获取所有登录权限相关的设置: 1-强制安全登录, 2-IMAP/SMTP, 3-POP/SMTP, 4-安全登录
        result = await api_service.get_user_option(member.email, [1, 2, 3, 4])
        
        if result.errcode != 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"获取登录权限失败: {result.errmsg}"
            )
        
        return EmailUserOptionResponse(
            errcode=result.errcode,
            errmsg=result.errmsg,
            option=result.data.get("option", []) if result.data else []
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"API调用失败: {str(e)}"
        )

@router.post("/members/batch-login-permissions")
async def batch_get_member_login_permissions(
    request: Dict[str, List[str]],
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:view"]))
):
    """批量获取成员登录权限设置"""
    userids = request.get("userids", [])
    if not userids:
        return {"data": {}}
    
    try:
        # 使用功能设置应用的配置来管理登录权限
        api_service = TencentEmailAPIService(db, app_name="功能设置")
        
        # 使用批量获取方法
        results = await api_service.get_users_options_batch(
            userids, [1, 2, 3, 4], batch_size=20, max_concurrent=5
        )
        
        # 处理结果
        processed_results = {}
        for userid, result in results.items():
            if result.errcode == 0 and result.data:
                options = result.data.get("option", [])
                permissions = {
                    "force_secure_login": 0,
                    "imap_smtp_enabled": 0,
                    "pop_smtp_enabled": 0,
                    "secure_login_enabled": 0
                }
                
                for option in options:
                    option_type = option.get("type")
                    value = int(option.get("value", "0"))
                    
                    if option_type == 1:
                        permissions["force_secure_login"] = value
                    elif option_type == 2:
                        permissions["imap_smtp_enabled"] = value
                    elif option_type == 3:
                        permissions["pop_smtp_enabled"] = value
                    elif option_type == 4:
                        permissions["secure_login_enabled"] = value
                
                processed_results[userid] = permissions
        
        return {
            "success": True,
            "data": processed_results
        }
        
    except Exception as e:
        logger.error(f"批量获取成员登录权限失败: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"批量获取成员登录权限失败: {str(e)}",
            "data": {}
        }

@router.put("/members/{userid}/login-permissions")
async def update_member_login_permissions(
    userid: str,
    permissions: EmailLoginPermissionUpdate,
    sync_to_api: bool = True,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:update"]))
):
    """更新成员登录权限设置"""
    # 根据邮箱地址（userid）查找成员
    member = email_member.get_by_email(db, email=userid)
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )
    
    # 准备选项设置
    options = []
    if permissions.force_secure_login is not None:
        options.append({"type": 1, "value": str(permissions.force_secure_login)})
    if permissions.imap_smtp_enabled is not None:
        options.append({"type": 2, "value": str(permissions.imap_smtp_enabled)})
    if permissions.pop_smtp_enabled is not None:
        options.append({"type": 3, "value": str(permissions.pop_smtp_enabled)})
    if permissions.secure_login_enabled is not None:
        options.append({"type": 4, "value": str(permissions.secure_login_enabled)})
    
    # 如果需要同步到API
    if sync_to_api and options:
        try:
            # 使用功能设置应用的配置来管理登录权限
            api_service = TencentEmailAPIService(db, app_name="功能设置")
            result = await api_service.set_user_option(member.email, options)
            
            if result.errcode != 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"设置登录权限失败: {result.errmsg}"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"API调用失败: {str(e)}"
            )
    
    # 更新本地数据库记录
    update_data = permissions.dict(exclude_unset=True)
    if update_data:
        # 直接更新字段，避免 CRUD 方法的类型问题
        for field, value in update_data.items():
            setattr(member, field, value)
        db.add(member)
        db.commit()
        db.refresh(member)
    
    return {
        "message": "登录权限更新成功",
        "member_id": member.id,
        "userid": member.email,
        "updated_permissions": update_data
    }

@router.post("/members/{userid}/sync-login-permissions")
async def sync_member_login_permissions(
    userid: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:member:sync"]))
):
    """同步成员登录权限设置（从腾讯API获取最新设置并更新本地数据库）"""
    # 根据邮箱地址（userid）查找成员
    member = email_member.get_by_email(db, email=userid)
    if not member:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成员不存在"
        )
    
    try:
        # 使用功能设置应用的配置来管理登录权限
        api_service = TencentEmailAPIService(db, app_name="功能设置")
        # 获取所有登录权限相关的设置
        result = await api_service.get_user_option(member.email, [1, 2, 3, 4])
        
        if result.errcode != 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"获取登录权限失败: {result.errmsg}"
            )
        
        # 解析API返回的选项设置
        options = result.data.get("option", []) if result.data else []
        update_data = {}
        
        for option in options:
            option_type = option.get("type")
            value = int(option.get("value", "0"))
            
            if option_type == 1:
                update_data["force_secure_login"] = value
            elif option_type == 2:
                update_data["imap_smtp_enabled"] = value
            elif option_type == 3:
                update_data["pop_smtp_enabled"] = value
            elif option_type == 4:
                update_data["secure_login_enabled"] = value
        
        # 更新本地数据库
        if update_data:
            for field, value in update_data.items():
                setattr(member, field, value)
            db.add(member)
            db.commit()
            db.refresh(member)
        
        return {
            "message": "登录权限同步成功",
            "member_id": member.id,
            "userid": member.email,
            "synced_permissions": update_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"API调用失败: {str(e)}"
        )

@router.post("/sync/groups")
async def sync_groups_from_api(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:group:sync"]))
):
    """从API同步群组数据"""
    try:
        # 记录开始时间
        start_time = time.time()
        
        api_service = TencentEmailAPIService(db, app_name="通讯录管理")
        # 使用群组列表获取方法
        result = await api_service.get_group_list()
        
        if result.errcode != 0:
            # 如果群组列表功能不可用，返回友好提示
            if "暂不可用" in result.errmsg:
                return {
                    "message": "群组同步功能暂不可用：腾讯企业邮箱API可能不支持群组列表获取",
                    "synced_count": 0,
                    "updated_count": 0,
                    "total_count": 0,
                    "elapsed_time": "0.00秒",
                    "status": "not_supported"
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"API调用失败: {result.errmsg}"
                )
        
        groups_data = result.data.get("grouplist", []) if result.data else []
        synced_count = 0
        updated_count = 0
        
        # 分批处理群组数据
        batch_size = 50
        total_groups = len(groups_data)
        
        logger.info(f"开始同步群组，共 {total_groups} 个群组")
        
        for i in range(0, total_groups, batch_size):
            batch_groups = groups_data[i:i+batch_size]
            logger.info(f"处理群组批次 {i//batch_size + 1}/{(total_groups + batch_size - 1)//batch_size}，包含 {len(batch_groups)} 个群组")
            
            batch_synced = 0
            batch_updated = 0
            
            for group_data in batch_groups:
                groupid = group_data.get("groupid")
                if not groupid:
                    continue
                    
                existing = email_group.get_by_groupid(db, groupid=groupid)
                
                if not existing:
                    # 创建新群组
                    group_create = EmailGroupCreate(
                        groupid=groupid,
                        groupname=group_data.get("groupname", ""),
                        userlist=",".join(group_data.get("userlist", [])),
                        groupdesc=group_data.get("groupdesc", ""),
                        is_active=True
                    )
                    email_group.create(db, obj_in=group_create)
                    batch_synced += 1
                else:
                    # 更新现有群组
                    group_update = EmailGroupUpdate(
                        groupname=group_data.get("groupname"),
                        userlist=",".join(group_data.get("userlist", [])),
                        groupdesc=group_data.get("groupdesc", "")
                    )
                    email_group.update(db, db_obj=existing, obj_in=group_update)
                    batch_updated += 1
            
            # 每批次处理完成后提交事务
            if batch_synced > 0 or batch_updated > 0:
                db.commit()
                synced_count += batch_synced
                updated_count += batch_updated
                logger.info(f"本批次同步了 {batch_synced} 个新群组，更新了 {batch_updated} 个群组")
        
        # 计算总耗时
        elapsed_time = time.time() - start_time
        
        return {
            "message": f"成功同步 {synced_count} 个群组，更新 {updated_count} 个群组，总耗时: {elapsed_time:.2f}秒",
            "synced_count": synced_count,
            "updated_count": updated_count,
            "total_count": total_groups,
            "elapsed_time": f"{elapsed_time:.2f}秒"
        }
        
    except Exception as e:
        # 发生异常时回滚事务
        db.rollback()
        logger.error(f"同步群组失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步失败: {str(e)}"
        )

@router.post("/sync/tags")
async def sync_tags_from_api(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:tag:sync"]))
):
    """从API同步标签数据"""
    try:
        # 记录开始时间
        start_time = time.time()
        
        api_service = TencentEmailAPIService(db, app_name="通讯录管理")
        result = await api_service.get_tag_list()
        
        if result.errcode != 0:
            # 检查特定错误码，提供更友好的错误信息
            if result.errcode == -1000888:
                return {
                    "message": "标签同步失败：应用权限不足或标签功能未启用，请检查企业邮箱应用配置",
                    "synced_count": 0,
                    "updated_count": 0,
                    "total_count": 0,
                    "elapsed_time": "0.00秒",
                    "status": "permission_denied",
                    "error_code": result.errcode
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"API调用失败: {result.errmsg} (错误码: {result.errcode})"
                )
        
        tags_data = result.data.get("taglist", []) if result.data else []
        synced_count = 0
        updated_count = 0
        
        # 分批处理标签数据
        batch_size = 50
        total_tags = len(tags_data)
        
        logger.info(f"开始同步标签，共 {total_tags} 个标签")
        
        for i in range(0, total_tags, batch_size):
            batch_tags = tags_data[i:i+batch_size]
            logger.info(f"处理标签批次 {i//batch_size + 1}/{(total_tags + batch_size - 1)//batch_size}，包含 {len(batch_tags)} 个标签")
            
            batch_synced = 0
            batch_updated = 0
            
            for tag_data in batch_tags:
                tagid = tag_data.get("tagid")
                if not tagid:
                    continue
                    
                existing = email_tag.get_by_tagid(db, tagid=tagid)
                
                if not existing:
                    # 创建新标签
                    tag_create = EmailTagCreate(
                        tagid=tagid,
                        tagname=tag_data.get("tagname", ""),
                        is_active=True
                    )
                    email_tag.create(db, obj_in=tag_create)
                    batch_synced += 1
                else:
                    # 更新现有标签
                    tag_update = EmailTagUpdate(
                        tagname=tag_data.get("tagname")
                    )
                    email_tag.update(db, db_obj=existing, obj_in=tag_update)
                    batch_updated += 1
            
            # 每批次处理完成后提交事务
            if batch_synced > 0 or batch_updated > 0:
                db.commit()
                synced_count += batch_synced
                updated_count += batch_updated
                logger.info(f"本批次同步了 {batch_synced} 个新标签，更新了 {batch_updated} 个标签")
        
        # 计算总耗时
        elapsed_time = time.time() - start_time
        
        return {
            "message": f"成功同步 {synced_count} 个标签，更新 {updated_count} 个标签，总耗时: {elapsed_time:.2f}秒",
            "synced_count": synced_count,
            "updated_count": updated_count,
            "total_count": total_tags,
            "elapsed_time": f"{elapsed_time:.2f}秒"
        }
        
    except Exception as e:
        # 发生异常时回滚事务
        db.rollback()
        logger.error(f"同步标签失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步失败: {str(e)}"
        )

# 同步日志管理
@router.get("/sync/logs", response_model=EmailSyncLogList)
async def get_sync_logs(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    sync_type: Optional[str] = Query(None, description="同步类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:view"]))
):
    """获取邮箱同步日志列表"""
    skip = (page - 1) * size
    result = email_sync_log.get_sync_logs(
        db=db, 
        skip=skip, 
        limit=size,
        sync_type=sync_type,
        status=status
    )
    return result

@router.get("/sync/logs/{log_id}", response_model=EmailSyncLog)
async def get_sync_log(
    log_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:view"]))
):
    """获取单个同步日志详情"""
    log = email_sync_log.get_sync_log_by_id(db=db, log_id=log_id)
    if not log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="同步日志不存在"
        )
    return log

@router.get("/sync/latest-times", response_model=LatestSyncTimes)
async def get_latest_sync_times(
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:view"]))
):
    """获取各个同步类型的最新同步时间"""
    times = email_sync_log.get_latest_sync_times(db=db)
    return times

@router.delete("/sync/logs/cleanup")
async def cleanup_old_logs(
    days: int = Query(30, ge=1, le=365, description="保留天数"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:sync:delete"]))
):
    """清理旧的同步日志"""
    deleted_count = email_sync_log.delete_old_logs(db=db, days=days)
    return {
        "message": f"成功清理 {deleted_count} 条 {days} 天前的同步日志",
        "deleted_count": deleted_count
    }

# 部门结构同步功能
@router.post("/sync/departments/from-personnel")
async def sync_departments_from_personnel(
    request: schemas.DepartmentSyncRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:sync"]))
):
    """从基础信息-人员信息同步部门结构到腾讯企业邮箱"""
    logger.info(f"用户 {current_user.email} 请求从基础信息同步部门结构")
    
    try:
        # 导入部门结构同步服务
        from app.services.department_structure_sync import DepartmentStructureSyncService
        
        # 创建同步服务实例
        sync_service = DepartmentStructureSyncService(db)
        
        # 执行同步
        result = await sync_service.sync_department_structure(request)
        
        # 记录同步日志
        log_data = {
            "sync_type": "departments_from_personnel",
            "status": "success" if result.success else "failed",
            "message": result.message,
            "synced_count": result.stats.created_departments,
            "updated_count": result.stats.updated_departments,
            "total_count": result.stats.total_departments,
            "duration": f"{result.duration:.2f}秒",
            "details": {
                "request": request.dict(),
                "stats": result.stats.dict(),
                "operations_count": len(result.operations),
                "errors": result.errors,
                "warnings": result.warnings
            },
            "error_message": "; ".join(result.errors) if result.errors else None
        }
        
        email_sync_log.create_sync_log(db, EmailSyncLogCreate(**log_data))
        
        return result
        
    except Exception as e:
        error_msg = f"部门结构同步失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        
        # 记录失败日志
        log_data = {
            "sync_type": "departments_from_personnel",
            "status": "failed",
            "message": error_msg,
            "synced_count": 0,
            "updated_count": 0,
            "total_count": 0,
            "duration": "0秒",
            "error_message": error_msg
        }
        email_sync_log.create_sync_log(db, EmailSyncLogCreate(**log_data))
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.post("/sync/departments/from-personnel/preview")
async def preview_departments_sync_from_personnel(
    request: schemas.DepartmentSyncRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:sync"]))
):
    """预览从基础信息同步部门结构（试运行模式）"""
    logger.info(f"用户 {current_user.email} 请求预览部门结构同步")
    
    try:
        # 导入部门结构同步服务
        from app.services.department_structure_sync import DepartmentStructureSyncService
        
        # 创建同步服务实例
        sync_service = DepartmentStructureSyncService(db)
        
        # 执行预览
        result = await sync_service.preview_sync(request)
        
        return result
        
    except Exception as e:
        error_msg = f"预览部门结构同步失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )


@router.get("/departments/ecology-structure")
async def get_ecology_department_structure(
    company_id: Optional[int] = Query(None, description="公司ID筛选"),
    department_id: Optional[int] = Query(None, description="部门ID筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(deps.check_permissions(["email:department:view"]))
):
    """获取基础信息中的部门结构"""
    try:
        from app.services.department_structure_sync import DepartmentStructureSyncService
        from app.schemas.department_sync import DepartmentSyncRequest, DepartmentSyncSource
        
        # 构建请求参数
        request = DepartmentSyncRequest(
            source=DepartmentSyncSource.ALL,
            company_id=company_id,
            department_id=department_id
        )
        
        if company_id:
            request.source = DepartmentSyncSource.COMPANY
        elif department_id:
            request.source = DepartmentSyncSource.DEPARTMENT
        
        # 创建同步服务实例
        sync_service = DepartmentStructureSyncService(db)
        
        # 获取部门数据
        departments = await sync_service._get_departments_from_ecology(request)
        
        # 构建层级结构
        hierarchy = sync_service._build_department_hierarchy(departments)
        
        return {
            "success": True,
            "message": f"获取到 {len(departments)} 个部门",
            "total_departments": len(departments),
            "hierarchy": hierarchy,
            "departments": departments
        }
        
    except Exception as e:
        error_msg = f"获取部门结构失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )

 